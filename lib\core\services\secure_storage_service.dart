import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

/// Service pour le stockage sécurisé des données sensibles
class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Clés de stockage
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _refreshTokenKey = 'refresh_token';

  /// Sauvegarder le token d'authentification
  static Future<void> saveAuthToken(String token) async {
    try {
      await _storage.write(key: _tokenKey, value: token);
    } catch (e) {
      throw Exception('Erreur lors de la sauvegarde du token: $e');
    }
  }

  /// Récupérer le token d'authentification
  static Future<String?> getAuthToken() async {
    try {
      return await _storage.read(key: _token<PERSON>ey);
    } catch (e) {
      return null;
    }
  }

  /// Sauvegarder le refresh token
  static Future<void> saveRefreshToken(String refreshToken) async {
    try {
      await _storage.write(key: _refreshTokenKey, value: refreshToken);
    } catch (e) {
      throw Exception('Erreur lors de la sauvegarde du refresh token: $e');
    }
  }

  /// Récupérer le refresh token
  static Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: _refreshTokenKey);
    } catch (e) {
      return null;
    }
  }

  /// Sauvegarder les données utilisateur
  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    try {
      final userJson = jsonEncode(userData);
      await _storage.write(key: _userKey, value: userJson);
    } catch (e) {
      throw Exception('Erreur lors de la sauvegarde des données utilisateur: $e');
    }
  }

  /// Récupérer les données utilisateur
  static Future<Map<String, dynamic>?> getUserData() async {
    try {
      final userJson = await _storage.read(key: _userKey);
      if (userJson != null) {
        return jsonDecode(userJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Vérifier si l'utilisateur est authentifié
  static Future<bool> isAuthenticated() async {
    try {
      final token = await getAuthToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Supprimer toutes les données d'authentification
  static Future<void> clearAuthData() async {
    try {
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _refreshTokenKey);
      await _storage.delete(key: _userKey);
    } catch (e) {
      throw Exception('Erreur lors de la suppression des données: $e');
    }
  }

  /// Supprimer toutes les données stockées
  static Future<void> clearAll() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      throw Exception('Erreur lors de la suppression de toutes les données: $e');
    }
  }

  /// Sauvegarder une valeur personnalisée
  static Future<void> saveValue(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      throw Exception('Erreur lors de la sauvegarde de $key: $e');
    }
  }

  /// Récupérer une valeur personnalisée
  static Future<String?> getValue(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      return null;
    }
  }

  /// Supprimer une valeur personnalisée
  static Future<void> deleteValue(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw Exception('Erreur lors de la suppression de $key: $e');
    }
  }

  /// Vérifier si une clé existe
  static Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      return false;
    }
  }

  /// Obtenir toutes les clés stockées
  static Future<Map<String, String>> getAllValues() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      return {};
    }
  }
}
