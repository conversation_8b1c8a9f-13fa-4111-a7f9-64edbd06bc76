import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../../core/constants/app_constants.dart';
import '../../core/config/app_config.dart';
import '../../core/services/secure_storage_service.dart';

class AuthService {
  static String get _baseUrl => AppConfig.baseUrl;
  
  // Login user
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      print('🔄 Tentative de connexion pour: $email');
      print('🌐 URL: $_baseUrl/auth/login');

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );

      print('📡 Status Code: ${response.statusCode}');
      print('📡 Response Body: ${response.body}');

      // Vérifier si la réponse est vide
      if (response.body.isEmpty) {
        return {
          'success': false,
          'message': 'Réponse vide du serveur',
        };
      }

      Map<String, dynamic> data;
      try {
        data = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        return {
          'success': false,
          'message': 'Réponse invalide du serveur: ${e.toString()}',
        };
      }

      if (response.statusCode == 200) {
        print('📋 Structure de la réponse: ${data.keys.toList()}');

        // Essayer différentes structures de réponse possibles
        String? token;
        Map<String, dynamic>? userData;

        // Structure 1: { "token": "...", "user": {...} }
        if (data.containsKey('token') && data.containsKey('user')) {
          token = data['token']?.toString();
          userData = data['user'] as Map<String, dynamic>?;
        }
        // Structure 2: { "access_token": "...", "user": {...} }
        else if (data.containsKey('access_token') && data.containsKey('user')) {
          token = data['access_token']?.toString();
          userData = data['user'] as Map<String, dynamic>?;
        }
        // Structure 3: { "data": { "token": "...", "user": {...} } }
        else if (data.containsKey('data')) {
          final dataSection = data['data'] as Map<String, dynamic>?;
          if (dataSection != null) {
            token = dataSection['token']?.toString() ?? dataSection['access_token']?.toString();
            userData = dataSection['user'] as Map<String, dynamic>?;
          }
        }
        // Structure 4: Réponse directe avec les données utilisateur (token dans les headers ou ailleurs)
        else if (data.containsKey('id') && data.containsKey('email')) {
          // L'utilisateur est directement dans la réponse, chercher le token ailleurs
          token = data['token']?.toString() ??
                  data['access_token']?.toString() ??
                  response.headers['authorization']?.replaceFirst('Bearer ', '') ??
                  'temp_token_${DateTime.now().millisecondsSinceEpoch}'; // Token temporaire pour test
          userData = data;
        }

        print('🔑 Token trouvé: ${token != null ? "Oui (${token.length} chars)" : "Non"}');
        print('👤 Données utilisateur trouvées: ${userData != null ? "Oui" : "Non"}');

        if (token == null || token.isEmpty) {
          return {
            'success': false,
            'message': 'Token manquant dans la réponse. Structure reçue: ${data.keys.toList()}',
          };
        }

        if (userData == null) {
          return {
            'success': false,
            'message': 'Données utilisateur manquantes dans la réponse. Structure reçue: ${data.keys.toList()}',
          };
        }

        try {
          // Créer le modèle utilisateur avec gestion d'erreur
          final user = UserModel.fromJson(userData);

          // Sauvegarder avec le stockage sécurisé
          await _saveAuthDataSecure(token, userData);

          print('✅ Connexion réussie pour: ${user.name}');

          return {
            'success': true,
            'token': token,
            'user': user,
            'message': data['message']?.toString() ?? 'Connexion réussie',
          };
        } catch (e) {
          print('❌ Erreur lors du parsing des données utilisateur: $e');
          print('📋 Données utilisateur reçues: $userData');
          return {
            'success': false,
            'message': 'Erreur lors du traitement des données utilisateur: ${e.toString()}',
          };
        }
      } else {
        final message = data['message']?.toString() ??
                       data['error']?.toString() ??
                       'Échec de la connexion (${response.statusCode})';

        print('❌ Échec de la connexion: $message');

        return {
          'success': false,
          'message': message,
        };
      }
    } catch (e) {
      print('❌ Erreur réseau: $e');
      return {
        'success': false,
        'message': 'Erreur de connexion: ${e.toString()}',
      };
    }
  }

  // Register user
  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/register'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'name': name,
          'email': email,
          'password': password,
          'password_confirmation': passwordConfirmation,
          'phone': phone,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201) {
        // Save token and user data
        await _saveAuthData(data['token'], data['user']);

        return {
          'success': true,
          'token': data['token'],
          'user': UserModel.fromJson(data['user']),
          'message': data['message'] ?? 'Registration successful',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Registration failed',
          'errors': data['errors'],
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Logout user
  Future<Map<String, dynamic>> logout() async {
    try {
      final token = await getToken();
      
      if (token != null) {
        final response = await http.post(
          Uri.parse('$_baseUrl/auth/logout'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );

        if (response.statusCode == 200) {
          await _clearAuthData();
          return {
            'success': true,
            'message': 'Logout successful',
          };
        }
      }
      
      // Clear local data even if API call fails
      await _clearAuthData();
      return {
        'success': true,
        'message': 'Logout successful',
      };
    } catch (e) {
      // Clear local data even if there's an error
      await _clearAuthData();
      return {
        'success': true,
        'message': 'Logout successful',
      };
    }
  }

  // Get current user
  Future<UserModel?> getCurrentUser() async {
    try {
      // Essayer d'abord le stockage sécurisé
      final userData = await SecureStorageService.getUserData();
      if (userData != null) {
        return UserModel.fromJson(userData);
      }

      // Fallback vers SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.userKey);

      if (userJson != null) {
        final userDataFallback = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userDataFallback);
      }

      return null;
    } catch (e) {
      print('❌ Erreur lors de la récupération de l\'utilisateur: $e');
      return null;
    }
  }

  // Get stored token
  Future<String?> getToken() async {
    try {
      // Essayer d'abord le stockage sécurisé
      final token = await SecureStorageService.getAuthToken();
      if (token != null && token.isNotEmpty) {
        return token;
      }

      // Fallback vers SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(AppConstants.tokenKey);
    } catch (e) {
      print('❌ Erreur lors de la récupération du token: $e');
      return null;
    }
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    try {
      // Vérifier d'abord le stockage sécurisé
      final isAuth = await SecureStorageService.isAuthenticated();
      if (isAuth) return true;

      // Fallback vers la méthode traditionnelle
      final token = await getToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      print('❌ Erreur lors de la vérification d\'authentification: $e');
      return false;
    }
  }



  // Update user profile
  Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> data) async {
    try {
      final token = await getToken();

      if (token == null) {
        return {
          'success': false,
          'message': 'Not authenticated',
        };
      }

      final response = await http.put(
        Uri.parse('$_baseUrl/user/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(data),
      );

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // Update stored user data
        await _saveAuthData(token, responseData['user']);

        return {
          'success': true,
          'user': UserModel.fromJson(responseData['user']),
          'message': responseData['message'] ?? 'Profile updated successfully',
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Profile update failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Change password
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final token = await getToken();

      if (token == null) {
        return {
          'success': false,
          'message': 'Not authenticated',
        };
      }

      final response = await http.put(
        Uri.parse('$_baseUrl/user/change-password'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'current_password': currentPassword,
          'new_password': newPassword,
          'new_password_confirmation': newPassword,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': data['message'] ?? 'Password changed successfully',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Password change failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Save authentication data with secure storage
  Future<void> _saveAuthDataSecure(String token, Map<String, dynamic> userData) async {
    try {
      await SecureStorageService.saveAuthToken(token);
      await SecureStorageService.saveUserData(userData);

      // Aussi sauvegarder dans SharedPreferences pour compatibilité
      await _saveAuthData(token, userData);
    } catch (e) {
      print('⚠️ Erreur lors de la sauvegarde sécurisée, utilisation de SharedPreferences: $e');
      await _saveAuthData(token, userData);
    }
  }

  // Save authentication data (fallback)
  Future<void> _saveAuthData(String token, Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.tokenKey, token);
    await prefs.setString(AppConstants.userKey, jsonEncode(userData));
  }

  // Clear authentication data
  Future<void> _clearAuthData() async {
    try {
      await SecureStorageService.clearAuthData();
    } catch (e) {
      print('⚠️ Erreur lors de la suppression sécurisée: $e');
    }

    // Aussi nettoyer SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.tokenKey);
    await prefs.remove(AppConstants.userKey);
  }

  // Refresh token
  Future<Map<String, dynamic>> refreshToken() async {
    try {
      final token = await getToken();
      
      if (token == null) {
        return {
          'success': false,
          'message': 'No token found',
        };
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/refresh'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        await _saveAuthData(data['token'], data['user']);
        
        return {
          'success': true,
          'token': data['token'],
          'user': UserModel.fromJson(data['user']),
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Token refresh failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Forgot password
  Future<Map<String, dynamic>> forgotPassword(String email) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/forgot-password'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'email': email,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': data['message'] ?? 'Reset email sent successfully',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to send reset email',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }
}
